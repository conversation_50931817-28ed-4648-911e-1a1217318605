<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="RuleAssessTriggerForm">
          <a-row>
            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">制度信息</h1>
              </div>
            </a-col>

            <a-col :span="12">
              <a-form-item label="任务编号" v-bind="validateInfos.taskCode" id="RuleAssessTriggerForm-taskCode" name="taskCode">
                <a-input v-model:value="formData.taskCode" placeholder="请输入任务编号" :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务名制度后评估任务名称" v-bind="validateInfos.taskName" id="RuleAssessTriggerForm-taskName" name="taskName">
                <a-input v-model:value="formData.taskName" placeholder="请输入任务名制度后评估任务名称" :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="制度后评估类型" v-bind="validateInfos.assessType" id="RuleAssessTriggerForm-assessType" name="assessType">
                <j-dict-select-tag
                  v-model:value="formData.assessType"
                  dictCode="rule_assess_pilot_assess_type"
                  placeholder="请选择制度后评估类型"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务触发类型" v-bind="validateInfos.triggerType" id="RuleAssessTriggerForm-triggerType" name="triggerType">
                <j-dict-select-tag
                  v-model:value="formData.triggerType"
                  dictCode="rule_assess_trigger_type"
                  placeholder="请选择任务触发类型"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="触发原因" v-bind="validateInfos.triggerReason" id="RuleAssessTriggerForm-triggerReason" name="triggerReason">
                <j-dict-select-tag
                  v-model:value="formData.triggerReason"
                  dictCode="rule_assess_trigger_reason"
                  placeholder="请选择触发原因"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <!-- <a-col :span="24">
							<a-form-item label="关联制度ID" v-bind="validateInfos.systemId" id="RuleAssessTriggerForm-systemId" name="systemId">
								<a-input v-model:value="formData.systemId" placeholder="请输入关联制度ID"  allow-clear ></a-input>
							</a-form-item>
						</a-col> -->
            <a-col :span="12">
              <a-form-item label="待评估制度名称" v-bind="validateInfos.systemName" id="RuleAssessTriggerForm-systemName" name="systemName">
                <a-input v-model:value="formData.systemName" placeholder="请选择制度" :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评估任务结束时间" v-bind="validateInfos.endTime" id="RuleAssessTriggerForm-endTime" name="endTime">
                <a-date-picker
                  placeholder="请选择评估任务结束时间"
                  v-model:value="formData.endTime"
                  value-format="YYYY-MM-DD"
                  :disabled="true"
                  style="width: 100%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="任务发起机构"
                v-bind="validateInfos.initiateOrgCode"
                id="RuleAssessTriggerForm-initiateOrgCode"
                name="initiateOrgCode"
              >
                <j-select-dept
                  v-model:value="formData.initiateOrgCode"
                  rowKey="orgCode"
                  :multiple="true"
                  checkStrictly
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="任务发起部门"
                v-bind="validateInfos.initiateDeptCode"
                id="RuleAssessTriggerForm-initiateDeptCode"
                name="initiateDeptCode"
              >
                <j-select-dept
                  v-model:value="formData.initiateDeptCode"
                  rowKey="orgCode"
                  :multiple="true"
                  checkStrictly
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评估机构" v-bind="validateInfos.assessOrgCode" id="RuleAssessTriggerForm-assessOrgCode" name="assessOrgCode">
                <j-select-dept v-model:value="formData.assessOrgCode" rowKey="orgCode" :multiple="true" checkStrictly :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评估部门" v-bind="validateInfos.assessDeptCode" id="RuleAssessTriggerForm-assessDeptCode" name="assessDeptCode">
                <j-select-dept v-model:value="formData.assessDeptCode" rowKey="orgCode" :multiple="true" checkStrictly :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务状态" v-bind="validateInfos.taskStatus" id="RuleAssessTriggerForm-taskStatus" name="taskStatus">
                <j-dict-select-tag
                  v-model:value="formData.taskStatus"
                  dictCode="rule_assess_trigger_task_status"
                  placeholder="请选择任务状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="流程状态" v-bind="validateInfos.processStatus" id="RuleAssessTriggerForm-processStatus" name="processStatus">
                <j-dict-select-tag
                  v-model:value="formData.processStatus"
                  dictCode="rule_assess_trigger_process_status"
                  placeholder="请选择流程状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">关联数据明细</h1>
              </div>
            </a-col>

            <a-col :span="12">
              <a-form-item label="制度关联咨询数量" v-bind="validateInfos.consultCount" id="RuleAssessTriggerForm-consultCount" name="consultCount">
                <a-input-number v-model:value="formData.consultCount" placeholder="请输入制度关联咨询数量" :disabled="true" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="制度关联建议数量"
                v-bind="validateInfos.suggestionCount"
                id="RuleAssessTriggerForm-suggestionCount"
                name="suggestionCount"
              >
                <a-input-number v-model:value="formData.suggestionCount" placeholder="请输入制度关联建议数量" :disabled="true" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="制度关联历史数据数量"
                v-bind="validateInfos.historyCount"
                id="RuleAssessTriggerForm-historyCount"
                name="historyCount"
              >
                <a-input-number v-model:value="formData.historyCount" placeholder="请输入制度关联历史数据数量" :disabled="true" style="width: 100%" />
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">评估结论</h1>
              </div>
            </a-col>

            <a-col :span="12">
              <a-form-item label="评估结论" v-bind="validateInfos.assessResult" id="RuleAssessTriggerForm-assessResult" name="assessResult">
                <j-dict-select-tag
                  v-model:value="formData.assessResult"
                  dictCode="rule_assess_trigger_result"
                  placeholder="请选择评估结论"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="结论说明"
                v-bind="validateInfos.resultDescription"
                id="RuleAssessTriggerForm-resultDescription"
                name="resultDescription"
              >
                <a-textarea
                  v-model:value="formData.resultDescription"
                  :rows="CONSTANTS.TEXTAREA_ROWS"
                  :placeholder="CONSTANTS.RESULT_DESCRIPTION_PLACEHOLDER"
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">关联制度立改废申请</h1>
                <a-button v-if="!formDisabled" type="primary" size="small" @click="openReformedAndAbolishedSelectModal" style="margin-left: 16px">
                  关联
                </a-button>
              </div>
            </a-col>

            <!-- 隐藏的关联制度立改废ID字段 -->
            <a-form-item v-show="false" v-bind="validateInfos.systemProcessId" id="RuleAssessTriggerForm-systemProcessId" name="systemProcessId">
              <a-input v-model:value="formData.systemProcessId" />
            </a-form-item>

            <a-col :span="12">
              <a-form-item label="维护任务编号" id="RuleAssessTriggerForm-maintenanceTaskCode" name="maintenanceTaskCode">
                <a-input v-model:value="formData.maintenanceTaskCode" placeholder="维护任务编号" :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="维护任务类型" id="RuleAssessTriggerForm-maintenanceTaskType" name="maintenanceTaskType">
                <j-dict-select-tag
                  v-model:value="formData.maintenanceTaskType"
                  dictCode="rule_system_maintenance_tasks_type"
                  placeholder="维护任务类型"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="维护任务状态" id="RuleAssessTriggerForm-maintenanceTaskStatus" name="maintenanceTaskStatus">
                <j-dict-select-tag
                  v-model:value="formData.maintenanceTaskStatus"
                  dictCode="rule_system_maintenance_tasks_status"
                  placeholder="维护任务状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="流程状态" id="RuleAssessTriggerForm-maintenanceProcessStatus" name="maintenanceProcessStatus">
                <j-dict-select-tag
                  v-model:value="formData.maintenanceProcessStatus"
                  dictCode="rule_system_maintenance_process_status"
                  placeholder="流程状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>

    <!-- 制度立改废申请选择模态框 -->
    <RuleSystemReformedAndAbolishedSelectModal @register="registerReformedAndAbolishedModal" @success="handleReformedAndAbolishedSelect" />
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import { getValueType } from '/@/utils';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import RuleSystemReformedAndAbolishedSelectModal from './RuleSystemReformedAndAbolishedSelectModal.vue';
  import { useModal } from '/@/components/Modal';
  import { saveOrUpdateTrigger } from '../../RuleAssessTrigger.api';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);

  // 制度立改废申请选择模态框
  const [registerReformedAndAbolishedModal, { openModal: openReformedAndAbolishedModal }] = useModal();

  // 表单字段定义
  interface RuleAssessTriggerFormData {
    id: string;
    taskCode: string;
    taskName: string;
    assessType: string;
    triggerType: string;
    triggerReason: string;
    systemId: string;
    systemName: string;
    endTime: string;
    initiateOrgCode: string;
    initiateDeptCode: string;
    assessOrgCode: string;
    assessDeptCode: string;
    taskStatus: string;
    processStatus: string;
    consultCount: number | undefined;
    suggestionCount: number | undefined;
    historyCount: number | undefined;
    assessResult: string;
    resultDescription: string;
    systemProcessId: string;
    maintenanceTaskCode: string;
    maintenanceTaskType: string;
    maintenanceTaskStatus: string;
    maintenanceProcessStatus: string;
  }

  // 初始化表单数据
  const getInitialFormData = (): RuleAssessTriggerFormData => {
    return {
      id: '',
      taskCode: '',
      taskName: '',
      assessType: '',
      triggerType: '',
      triggerReason: '',
      systemId: '',
      systemName: '',
      endTime: '',
      initiateOrgCode: '',
      initiateDeptCode: '',
      assessOrgCode: '',
      assessDeptCode: '',
      taskStatus: '',
      processStatus: '',
      consultCount: undefined,
      suggestionCount: undefined,
      historyCount: undefined,
      assessResult: '',
      resultDescription: '',
      systemProcessId: '',
      maintenanceTaskCode: '',
      maintenanceTaskType: '',
      maintenanceTaskStatus: '',
      maintenanceProcessStatus: '',
    };
  };

  // 常量定义
  const CONSTANTS = {
    RESULT_DESCRIPTION_PLACEHOLDER: '请对评估结论进行说明，包括但不限于概括说明需修订的内容、需新增或废止制度的原因，或无需调整制度的具体原因。',
    TEXTAREA_ROWS: 3,
  };

  const formData = reactive(getInitialFormData());
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    assessResult: [{ required: true, message: '请选择评估结论!', trigger: 'change' }],
    resultDescription: [
      {
        validator: (_rule: any, value: any) => {
          // 当评估结论为"无需调整制度"时，结论说明必填
          if (formData.assessResult === '3' && (!value || value.trim() === '')) {
            return Promise.reject('当评估结论为"无需调整制度"时，结论说明为必填项!');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    return props.formDisabled;
  });

  // 表单是否禁用（用于模板中的条件判断）
  const formDisabled = computed(() => {
    return props.formDisabled;
  });

  /**
   * 编辑
   */
  function edit(record: any) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch (error: any) {
      if (error?.errorFields) {
        const firstField = error.errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(error);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdateTrigger(model, isUpdate.value)
      .then((res: any) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  /**
   * 打开制度立改废申请选择模态框
   */
  function openReformedAndAbolishedSelectModal() {
    openReformedAndAbolishedModal(true, {});
  }

  /**
   * 制度立改废申请选择回调
   */
  function handleReformedAndAbolishedSelect(selectedRecord: any) {
    if (selectedRecord) {
      formData.systemProcessId = selectedRecord.id;
      formData.maintenanceTaskCode = selectedRecord.taskNum || '';
      formData.maintenanceTaskType = selectedRecord.taskType || '';
      formData.maintenanceTaskStatus = selectedRecord.taskStatus || '';
      formData.maintenanceProcessStatus = selectedRecord.processStatus || '';
    }
  }

  defineExpose({
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
  .a-description-dataDetail {
    padding: 10px 10px 0;
    margin: 10px 10px 0;
  }

  .title-container {
    display: flex;
    align-items: center;
    margin-left: 10px;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .title-bar {
    width: 5px;
    height: 22px;
    background: #ffc53d;
    border-radius: 3px;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(255, 197, 61, 0.3);
  }

  .title-text {
    font-size: 14px;
    font-weight: 600;
    color: #1f1f1f;
    margin: 0;
    letter-spacing: 0.5px;
  }
</style>
