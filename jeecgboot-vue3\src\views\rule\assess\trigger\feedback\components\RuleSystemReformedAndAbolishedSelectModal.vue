<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="1200" @ok="handleSubmit">
    <div class="p-4">
      <!-- 查询区域 -->
      <div class="jeecg-basic-table-form-container mb-4">
        <a-form ref="formRef" @keyup.enter="reload" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
            <a-col :lg="8">
              <a-form-item name="taskCode">
                <template #label><span title="任务编号">任务编号</span></template>
                <a-input placeholder="请输入任务编号" v-model:value="queryParam.taskCode" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="systemName">
                <template #label><span title="制度名称">制度名称</span></template>
                <a-input placeholder="请输入制度名称" v-model:value="queryParam.systemName" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="type">
                <template #label><span title="任务类型">任务类型</span></template>
                <j-dict-select-tag
                  v-model:value="queryParam.type"
                  :dictCode="DICT_CODES.REFORMED_ABOLISHED_TYPE"
                  placeholder="请选择任务类型"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" @click="searchQuery" style="margin-right: 8px">查询</a-button>
              <a-button @click="searchReset">重置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <BasicTable @register="registerTable" :rowSelection="rowSelection">
        <template #action="{ record }">
          <TableAction :actions="getTableAction(record)" />
        </template>
      </BasicTable>
    </div>

    <!-- 制度立改废申请详情弹窗 -->
    <RuleSystemReformedAndAbolishedModal ref="detailModal" @success="handleDetailSuccess" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, TableAction } from '/@/components/Table';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import RuleSystemReformedAndAbolishedModal from '/@/views/rule/regulations/manage/reformedAndAbolished/components/RuleSystemReformedAndAbolishedModal.vue';
  import { reformedAbolishedColumns } from '../../RuleAssessTrigger.data';
  import { getReformedAbolishedList } from '../../RuleAssessTrigger.api';

  const emit = defineEmits(['register', 'success']);

  // 常量定义
  const CONSTANTS = {
    MODAL_TITLE: '选择制度立改废申请',
  };

  // 字典代码常量
  const DICT_CODES = {
    REFORMED_ABOLISHED_TYPE: 'rule_system_reformed_abolished_type',
  };

  // 制度立改废申请查询参数接口
  interface ReformedAbolishedQueryParam {
    taskCode: string;
    systemName: string;
    type: string;
  }

  // 初始化查询参数
  const getInitialQueryParam = (): ReformedAbolishedQueryParam => {
    return {
      taskCode: '',
      systemName: '',
      type: '',
    };
  };

  const title = ref(CONSTANTS.MODAL_TITLE);
  const formRef = ref();
  const detailModal = ref();
  const queryParam = reactive(getInitialQueryParam());

  const labelCol = { span: 6 };
  const wrapperCol = { span: 18 };

  // 使用从data.ts导入的表格列定义
  const columns = reformedAbolishedColumns;

  // 表格配置
  const {
    tableContext: [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }],
  } = useListPage({
    tableProps: {
      api: getReformedAbolishedList,
      columns,
      useSearchForm: false,
      showTableSetting: false,
      tableSetting: { fullScreen: true },
      showIndexColumn: false,
      actionColumn: {
        title: '操作',
        width: 120,
        fixed: 'right',
      },
      rowKey: 'id',
      rowSelection: {
        type: 'radio',
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });

  // 弹窗注册
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async () => {
    setModalProps({ confirmLoading: false });
    // 重置查询条件
    Object.keys(queryParam).forEach((key) => {
      queryParam[key] = '';
    });
    selectedRowKeys.value = [];
    reload();
  });

  // 查询
  function searchQuery() {
    reload();
  }

  // 重置
  function searchReset() {
    Object.keys(queryParam).forEach((key) => {
      queryParam[key] = '';
    });
    reload();
  }

  // 操作列配置
  function getTableAction(record: any) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  // 查看详情
  function handleDetail(record: any) {
    if (detailModal.value) {
      detailModal.value.disableSubmit = true;
      detailModal.value.edit(record);
    }
  }

  // 详情弹窗成功回调
  function handleDetailSuccess() {
    // 详情弹窗关闭后的处理，如果需要的话
  }

  // 提交选择
  function handleSubmit() {
    if (selectedRows.value.length === 0) {
      return;
    }

    const selectedRecord = selectedRows.value[0];
    emit('success', selectedRecord);
    closeModal();
  }
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }
</style>
