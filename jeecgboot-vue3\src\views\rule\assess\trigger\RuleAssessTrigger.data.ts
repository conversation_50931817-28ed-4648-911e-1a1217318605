import { BasicColumn } from '/@/components/Table';

// 触发式评估任务管理列表表格列定义
export const columns: BasicColumn[] = [
  {
    title: '任务编号',
    align: 'center',
    dataIndex: 'taskCode',
  },
  {
    title: '任务名制度后评估任务名称',
    align: 'center',
    dataIndex: 'taskName',
  },
  {
    title: '制度后评估类型',
    align: 'center',
    dataIndex: 'assessType_dictText',
  },
  {
    title: '任务触发类型',
    align: 'center',
    dataIndex: 'triggerType_dictText',
  },
  {
    title: '触发原因',
    align: 'center',
    dataIndex: 'triggerReason_dictText',
  },
  {
    title: '待评估制度名称',
    align: 'center',
    dataIndex: 'systemName',
  },
  {
    title: '评估任务结束时间',
    align: 'center',
    dataIndex: 'endTime',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '任务发起机构',
    align: 'center',
    dataIndex: 'initiateOrgCode_dictText',
  },
  {
    title: '任务发起部门',
    align: 'center',
    dataIndex: 'initiateDeptCode_dictText',
  },
  {
    title: '评估机构',
    align: 'center',
    dataIndex: 'assessOrgCode_dictText',
  },
  {
    title: '评估部门',
    align: 'center',
    dataIndex: 'assessDeptCode_dictText',
  },
  {
    title: '任务状态',
    align: 'center',
    dataIndex: 'taskStatus_dictText',
  },
  {
    title: '流程状态',
    align: 'center',
    dataIndex: 'processStatus_dictText',
  },
  {
    title: '制度关联咨询数量',
    align: 'center',
    dataIndex: 'consultCount',
  },
  {
    title: '制度关联建议数量',
    align: 'center',
    dataIndex: 'suggestionCount',
  },
  {
    title: '制度关联历史数据数量',
    align: 'center',
    dataIndex: 'historyCount',
  },
  {
    title: '评估结论',
    align: 'center',
    dataIndex: 'assessResult_dictText',
  },
  {
    title: '结论说明',
    align: 'center',
    dataIndex: 'resultDescription',
  },
  {
    title: '维护任务编号',
    dataIndex: 'maintenanceTaskCode',
    width: 150,
  },
  {
    title: '维护任务类型',
    dataIndex: 'maintenanceTaskType_dictText',
    width: 120,
  },
  {
    title: '维护任务状态',
    dataIndex: 'maintenanceTaskStatus_dictText',
    width: 120,
  },
  {
    title: '维护任务流程状态',
    dataIndex: 'maintenanceProcessStatus_dictText',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
];

// 制度立改废申请选择弹窗表格列定义
export const reformedAbolishedColumns: BasicColumn[] = [
  {
    title: '任务编号',
    dataIndex: 'taskNum',
    width: 150,
  },
  {
    title: '制度名称',
    dataIndex: 'systemName',
    width: 200,
  },
  {
    title: '任务类型',
    dataIndex: 'taskType_dictText',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'taskStatus_dictText',
    width: 100,
  },
  {
    title: '流程状态',
    dataIndex: 'processStatus_dictText',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
];
