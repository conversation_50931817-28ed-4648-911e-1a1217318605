<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="RuleAssessTriggerForm">
          <a-row>
            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">制度信息</h1>
              </div>
            </a-col>

            <a-col :span="12">
              <a-form-item label="任务编号" v-bind="validateInfos.taskCode" id="RuleAssessTriggerForm-taskCode" name="taskCode">
                <a-input v-model:value="formData.taskCode" placeholder="请输入任务编号" :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务名制度后评估任务名称" v-bind="validateInfos.taskName" id="RuleAssessTriggerForm-taskName" name="taskName">
                <a-input v-model:value="formData.taskName" placeholder="请输入任务名制度后评估任务名称" :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="制度后评估类型" v-bind="validateInfos.assessType" id="RuleAssessTriggerForm-assessType" name="assessType">
                <j-dict-select-tag
                  v-model:value="formData.assessType"
                  dictCode="rule_assess_pilot_assess_type"
                  placeholder="请选择制度后评估类型"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务触发类型" v-bind="validateInfos.triggerType" id="RuleAssessTriggerForm-triggerType" name="triggerType">
                <j-dict-select-tag
                  v-model:value="formData.triggerType"
                  dictCode="rule_assess_trigger_type"
                  placeholder="请选择任务触发类型"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="触发原因" v-bind="validateInfos.triggerReason" id="RuleAssessTriggerForm-triggerReason" name="triggerReason">
                <j-dict-select-tag
                  v-model:value="formData.triggerReason"
                  dictCode="rule_assess_trigger_reason"
                  placeholder="请选择触发原因"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <!-- <a-col :span="24">
							<a-form-item label="关联制度ID" v-bind="validateInfos.systemId" id="RuleAssessTriggerForm-systemId" name="systemId">
								<a-input v-model:value="formData.systemId" placeholder="请输入关联制度ID"  allow-clear ></a-input>
							</a-form-item>
						</a-col> -->
            <a-col :span="12">
              <a-form-item label="待评估制度名称" v-bind="validateInfos.systemName" id="RuleAssessTriggerForm-systemName" name="systemName">
                <a-input
                  v-model:value="formData.systemName"
                  placeholder="请选择制度"
                  readonly
                  @click="openSystemSelectModal"
                  style="cursor: pointer"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评估任务结束时间" v-bind="validateInfos.endTime" id="RuleAssessTriggerForm-endTime" name="endTime">
                <a-date-picker
                  placeholder="请选择评估任务结束时间"
                  v-model:value="formData.endTime"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="任务发起机构"
                v-bind="validateInfos.initiateOrgCode"
                id="RuleAssessTriggerForm-initiateOrgCode"
                name="initiateOrgCode"
              >
                <j-select-dept
                  v-model:value="formData.initiateOrgCode"
                  rowKey="orgCode"
                  :multiple="true"
                  checkStrictly
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="任务发起部门"
                v-bind="validateInfos.initiateDeptCode"
                id="RuleAssessTriggerForm-initiateDeptCode"
                name="initiateDeptCode"
              >
                <j-select-dept
                  v-model:value="formData.initiateDeptCode"
                  rowKey="orgCode"
                  :multiple="true"
                  checkStrictly
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评估机构" v-bind="validateInfos.assessOrgCode" id="RuleAssessTriggerForm-assessOrgCode" name="assessOrgCode">
                <j-select-dept v-model:value="formData.assessOrgCode" rowKey="orgCode" :multiple="true" checkStrictly :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评估部门" v-bind="validateInfos.assessDeptCode" id="RuleAssessTriggerForm-assessDeptCode" name="assessDeptCode">
                <j-select-dept v-model:value="formData.assessDeptCode" rowKey="orgCode" :multiple="true" checkStrictly :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务状态" v-bind="validateInfos.taskStatus" id="RuleAssessTriggerForm-taskStatus" name="taskStatus">
                <j-dict-select-tag
                  v-model:value="formData.taskStatus"
                  dictCode="rule_assess_trigger_task_status"
                  placeholder="请选择任务状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="流程状态" v-bind="validateInfos.processStatus" id="RuleAssessTriggerForm-processStatus" name="processStatus">
                <j-dict-select-tag
                  v-model:value="formData.processStatus"
                  dictCode="rule_assess_trigger_process_status"
                  placeholder="请选择流程状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">关联数据明细</h1>
              </div>
            </a-col>

            <a-col :span="12">
              <a-form-item label="制度关联咨询数量" v-bind="validateInfos.consultCount" id="RuleAssessTriggerForm-consultCount" name="consultCount">
                <a-input-number v-model:value="formData.consultCount" placeholder="请输入制度关联咨询数量" :disabled="true" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="制度关联建议数量"
                v-bind="validateInfos.suggestionCount"
                id="RuleAssessTriggerForm-suggestionCount"
                name="suggestionCount"
              >
                <a-input-number v-model:value="formData.suggestionCount" placeholder="请输入制度关联建议数量" :disabled="true" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="制度关联历史数据数量"
                v-bind="validateInfos.historyCount"
                id="RuleAssessTriggerForm-historyCount"
                name="historyCount"
              >
                <a-input-number v-model:value="formData.historyCount" placeholder="请输入制度关联历史数据数量" :disabled="true" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row v-if="disabled">
            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">评估结论</h1>
              </div>
            </a-col>

            <a-col :span="12">
              <a-form-item label="评估结论" v-bind="validateInfos.assessResult" id="RuleAssessTriggerForm-assessResult" name="assessResult">
                <j-dict-select-tag
                  v-model:value="formData.assessResult"
                  dictCode="rule_assess_trigger_result"
                  placeholder="请选择评估结论"
                  allow-clear
                  :disabled="true"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="结论说明"
                v-bind="validateInfos.resultDescription"
                id="RuleAssessTriggerForm-resultDescription"
                name="resultDescription"
              >
                <a-textarea
                  v-model:value="formData.resultDescription"
                  :rows="3"
                  :placeholder="'请对评估结论进行说明，包括但不限于概括说明需修订的内容、需新增或废止制度的原因，或无需调整制度的具体原因'"
                  allow-clear
                  :disabled="true"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row v-if="disabled">
            <a-col :span="24">
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">关联制度立改废申请</h1>
                <a-button v-if="!formDisabled" type="primary" size="small" style="margin-left: 16px"> 关联 </a-button>
              </div>
            </a-col>

            <!-- 隐藏的关联制度立改废ID字段 -->
            <a-form-item v-show="false" v-bind="validateInfos.systemProcessId" id="RuleAssessTriggerForm-systemProcessId" name="systemProcessId">
              <a-input v-model:value="formData.systemProcessId" />
            </a-form-item>

            <a-col :span="12">
              <a-form-item label="维护任务编号" id="RuleAssessTriggerForm-maintenanceTaskCode" name="maintenanceTaskCode">
                <a-input v-model:value="formData.maintenanceTaskCode" placeholder="维护任务编号" :disabled="true" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="维护任务类型" id="RuleAssessTriggerForm-maintenanceTaskType" name="maintenanceTaskType">
                <j-dict-select-tag
                  v-model:value="formData.maintenanceTaskType"
                  dictCode="rule_system_maintenance_tasks_type"
                  placeholder="维护任务类型"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="维护任务状态" id="RuleAssessTriggerForm-maintenanceTaskStatus" name="maintenanceTaskStatus">
                <j-dict-select-tag
                  v-model:value="formData.maintenanceTaskStatus"
                  dictCode="rule_system_maintenance_tasks_status"
                  placeholder="维护任务状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="流程状态" id="RuleAssessTriggerForm-maintenanceProcessStatus" name="maintenanceProcessStatus">
                <j-dict-select-tag
                  v-model:value="formData.maintenanceProcessStatus"
                  dictCode="rule_system_maintenance_process_status"
                  placeholder="流程状态"
                  :disabled="true"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>

    <!-- 制度选择模态框 -->
    <RuleSystemSelectModal @register="registerSystemModal" @success="handleSystemSelect" />
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate, generateTaskCode } from '../../RuleAssessTrigger.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import RuleSystemSelectModal from '/@/views/rule/system/component/RuleSystemSelectModal.vue';
  import { useModal } from '/@/components/Modal';
  import { useUserStore } from '/@/store/modules/user';
  import { defHttp } from '/@/utils/http/axios';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const userStore = useUserStore();

  // 制度选择模态框
  const [registerSystemModal, { openModal: openSystemModal }] = useModal();
  const formData = reactive<Record<string, any>>({
    id: '',
    taskCode: '',
    taskName: '',
    assessType: '',
    triggerType: '',
    triggerReason: '',
    systemId: '',
    systemName: '',
    endTime: '',
    initiateOrgCode: '',
    initiateDeptCode: '',
    assessOrgCode: '',
    assessDeptCode: '',
    taskStatus: '',
    processStatus: '',
    consultCount: undefined,
    suggestionCount: undefined,
    historyCount: undefined,
    assessResult: '',
    resultDescription: '',
    systemProcessId: '',
    maintenanceTaskCode: '',
    maintenanceTaskType: '',
    maintenanceTaskStatus: '',
    maintenanceProcessStatus: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  const isAddMode = ref<boolean>(false); // 是否为新增模式
  //表单验证
  const validatorRules = reactive({
    taskCode: [{ required: true, message: '请输入任务编号!', trigger: 'blur' }],
    taskName: [{ required: true, message: '请输入任务名称!', trigger: 'blur' }],
    assessType: [{ required: true, message: '请选择制度后评估类型!', trigger: 'change' }],
    triggerType: [{ required: true, message: '请选择任务触发类型!', trigger: 'change' }],
    triggerReason: [{ required: true, message: '请选择触发原因!', trigger: 'change' }],
    systemId: [{ required: true, message: '请选择待评估制度!', trigger: 'change' }],
    systemName: [{ required: true, message: '请选择待评估制度!', trigger: 'blur' }],
    endTime: [
      { required: true, message: '请选择评估任务结束时间!', trigger: 'change' },
      {
        validator: (_rule: any, value: any) => {
          if (value) {
            const selectedDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (selectedDate < today) {
              return Promise.reject('评估任务结束时间不能早于今天!');
            }
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    initiateOrgCode: [{ required: true, message: '请选择任务发起机构!', trigger: 'change' }],
    initiateDeptCode: [{ required: true, message: '请选择任务发起部门!', trigger: 'change' }],
    assessOrgCode: [{ required: true, message: '请选择评估机构!', trigger: 'change' }],
    assessDeptCode: [{ required: true, message: '请选择评估部门!', trigger: 'change' }],
    taskStatus: [{ required: true, message: '请选择任务状态!', trigger: 'change' }],
    processStatus: [{ required: true, message: '请选择流程状态!', trigger: 'change' }],
    consultCount: [
      { required: true, message: '制度关联咨询数量不能为空!', trigger: 'blur' },
      { type: 'number', min: 0, message: '制度关联咨询数量必须大于等于0!', trigger: 'blur' },
    ],
    suggestionCount: [
      { required: true, message: '制度关联建议数量不能为空!', trigger: 'blur' },
      { type: 'number', min: 0, message: '制度关联建议数量必须大于等于0!', trigger: 'blur' },
    ],
    historyCount: [
      { required: true, message: '制度关联历史数据数量不能为空!', trigger: 'blur' },
      { type: 'number', min: 0, message: '制度关联历史数据数量必须大于等于0!', trigger: 'blur' },
    ],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    return props.formDisabled;
  });

  /**
   * 新增
   */
  async function add() {
    isAddMode.value = true;
    try {
      // 自动生成任务编号
      const taskCodeRes = await generateTaskCode();
      const userInfo = userStore.getUserInfo;

      const defaultData = {
        taskCode: taskCodeRes.success ? taskCodeRes.result : '',
        assessType: '3', // 制度后评估类型默认为触发式评估（3）
        triggerType: '2', // 任务触发类型默认为人工发起（2）
        taskStatus: '1', // 任务状态默认值为1
        processStatus: '1', // 流程状态默认值为1
        initiateOrgCode: userInfo.orgCode || '', // 任务发起机构默认为当前用户登录部门
        initiateDeptCode: userInfo.orgCode || '', // 任务发起部门默认为当前用户登录部门
      };

      edit(defaultData);
    } catch (error) {
      console.error('生成任务编号失败:', error);
      edit({});
    }
  }

  /**
   * 编辑
   */
  function edit(record) {
    if (record && record.id) {
      isAddMode.value = false;
    }
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  /**
   * 打开制度选择模态框
   */
  function openSystemSelectModal() {
    openSystemModal(true, {});
  }

  /**
   * 制度选择回调
   */
  async function handleSystemSelect(selectedSystem: any) {
    if (selectedSystem) {
      formData.systemId = selectedSystem.id;
      formData.systemName = selectedSystem.systemName;

      // 自动生成任务名称
      if (selectedSystem.systemName) {
        formData.taskName = `《${selectedSystem.systemName}》触发式评估任务`;
      }

      // 设置评估机构和评估部门为制度的发布机构和发布部门
      if (selectedSystem.systemIssuingBody) {
        formData.assessOrgCode = selectedSystem.systemIssuingBody;
      }
      if (selectedSystem.issuingDeptOne) {
        formData.assessDeptCode = selectedSystem.issuingDeptOne;
      }

      // 获取制度关联数据数量
      await loadSystemRelatedCounts(selectedSystem.id);
    }
  }

  /**
   * 获取制度关联数据数量
   */
  async function loadSystemRelatedCounts(systemId: string) {
    try {
      // 并行获取三个数量
      const [consultRes, suggestRes, historyRes] = await Promise.all([
        defHttp.get({ url: `/rule/system/consult/countBySystemId`, params: { systemId } }, { isTransformResponse: false }),
        defHttp.get({ url: `/rule/system/suggest/countBySystemId`, params: { systemId } }, { isTransformResponse: false }),
        defHttp.get({ url: `/rule/system/history/countBySystemId`, params: { systemId } }, { isTransformResponse: false }),
      ]);

      // 设置数量值
      if (consultRes.success) {
        formData.consultCount = consultRes.result || 0;
      }
      if (suggestRes.success) {
        formData.suggestionCount = suggestRes.result || 0;
      }
      if (historyRes.success) {
        formData.historyCount = historyRes.result || 0;
      }
    } catch (error) {
      console.error('获取制度关联数据数量失败:', error);
      // 设置默认值
      formData.consultCount = 0;
      formData.suggestionCount = 0;
      formData.historyCount = 0;
    }
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
  .a-description-dataDetail {
    padding: 10px 10px 0;
    margin: 10px 10px 0;
  }

  .title-container {
    display: flex;
    align-items: center;
    margin-left: 10px;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .title-bar {
    width: 5px;
    height: 22px;
    background: #ffc53d;
    border-radius: 3px;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(255, 197, 61, 0.3);
  }

  .title-text {
    font-size: 14px;
    font-weight: 600;
    color: #1f1f1f;
    margin: 0;
    letter-spacing: 0.5px;
  }
</style>
