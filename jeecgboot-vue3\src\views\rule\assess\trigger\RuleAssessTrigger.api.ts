import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

// API 接口地址枚举
enum Api {
  // 触发式评估相关接口
  list = '/rule/assess/trigger/list',
  listVO = '/rule/assess/trigger/listVO',
  save = '/rule/assess/trigger/add',
  edit = '/rule/assess/trigger/edit',
  deleteOne = '/rule/assess/trigger/delete',
  deleteBatch = '/rule/assess/trigger/deleteBatch',
  importExcel = '/rule/assess/trigger/importExcel',
  exportXls = '/rule/assess/trigger/exportXls',
  generateTaskCode = '/rule/assess/trigger/generateTaskCode',
  
  // 制度立改废申请相关接口
  reformedAbolishedList = '/rule/manage/reformedAndAbolished/ruleSystemReformedAndAbolished/list',
  reformedAbolishedDetail = '/rule/manage/reformedAndAbolished/ruleSystemReformedAndAbolished/queryById',
}

/**
 * 导出api
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 触发式评估任务列表查询
 * @param params 查询参数
 */
export const list = (params: any) => {
  return defHttp.get({ url: Api.list, params });
};

/**
 * 触发式评估任务VO列表查询（包含维护任务信息）
 * @param params 查询参数
 */
export const listVO = (params: any) => {
  return defHttp.get({ url: Api.listVO, params });
};

/**
 * 保存触发式评估任务
 * @param params 保存参数
 */
export const saveTrigger = (params: any) => {
  return defHttp.post({ url: Api.save, params }, { isTransformResponse: false });
};

/**
 * 编辑触发式评估任务
 * @param params 编辑参数
 */
export const editTrigger = (params: any) => {
  return defHttp.post({ url: Api.edit, params }, { isTransformResponse: false });
};

/**
 * 删除单个触发式评估任务
 * @param params 删除参数
 * @param handleSuccess 成功回调
 */
export const deleteOne = (params: any, handleSuccess?: () => void) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess && handleSuccess();
  });
};

/**
 * 批量删除触发式评估任务
 * @param params 批量删除参数
 * @param handleSuccess 成功回调
 */
export const batchDelete = (params: any, handleSuccess?: () => void) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess && handleSuccess();
      });
    }
  });
};

/**
 * 生成任务编号
 */
export const generateTaskCode = () => {
  return defHttp.get({ url: Api.generateTaskCode }, { isTransformResponse: false });
};

/**
 * 保存或更新触发式评估任务（统一保存接口）
 * @param params 保存参数
 * @param isUpdate 是否为更新操作
 */
export const saveOrUpdate = (params: any, isUpdate: boolean = false) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 查询制度立改废申请列表
 * @param params 查询参数
 */
export const getReformedAbolishedList = (params: any) => {
  try {
    return defHttp.get({
      url: Api.reformedAbolishedList,
      params,
    });
  } catch (error) {
    console.error('获取制度立改废申请列表失败:', error);
    return Promise.resolve({ records: [], total: 0 });
  }
};

/**
 * 查询制度立改废申请详情
 * @param id 申请ID
 */
export const getReformedAbolishedDetail = (id: string) => {
  return defHttp.get({
    url: Api.reformedAbolishedDetail,
    params: { id },
  });
};

// 兼容性导出
export const getTriggerList = list;
export const getTriggerListVO = listVO;
export const deleteTrigger = deleteOne;
export const deleteTriggerBatch = batchDelete;
export const saveOrUpdateTrigger = saveOrUpdate;

// 导出所有API方法
export default {
  list,
  listVO,
  saveTrigger,
  editTrigger,
  deleteOne,
  batchDelete,
  generateTaskCode,
  getReformedAbolishedList,
  getReformedAbolishedDetail,
  saveOrUpdate,
  saveOrUpdateTrigger,
  getTriggerList,
  getTriggerListVO,
  deleteTrigger,
  deleteTriggerBatch,
  getExportUrl,
  getImportUrl,
};
